<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格式化测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .formatted-text {
            background: white;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-line;
            line-height: 1.6;
        }
        .formatted-text strong {
            font-weight: 600;
            color: #1a5490;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>游戏攻略助手 - 格式化测试</h1>
    
    <div class="test-container">
        <h3>测试API调用和格式化</h3>
        <button onclick="testFormatting()">测试王者荣耀射手攻略</button>
        <button onclick="testFormatting2()">测试原神新手攻略</button>
        <button onclick="testFormatting3()">测试我的世界建筑技巧</button>
        
        <div id="result" class="formatted-text" style="margin-top: 20px; min-height: 100px;">
            点击按钮测试格式化效果...
        </div>
    </div>

    <script>
        // 格式化文本函数（与前端JS相同）
        function formatText(text) {
            if (!text) return '';
            
            let formattedText = text
                // 处理数字列表（1. 2. 3.）
                .replace(/(\d+\.\s*\*\*[^*]+\*\*)/g, '<br><br>$1')
                .replace(/(\d+\.\s*[^1-9\n]*?)(?=\d+\.|$)/g, '<br><br>$1')
                
                // 处理项目符号列表（✅ ❌ 等）
                .replace(/(✅|❌|🎮|📊|🔍|⚠️|💡|🎯|📋|🚀)\s*/g, '<br><br>$1 ')
                
                // 处理Markdown标题（### ## #）
                .replace(/###\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')
                .replace(/##\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')
                .replace(/#\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')
                
                // 处理粗体文本（**text**）
                .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                
                // 处理问号后的换行
                .replace(/([？?])\s*([1-9]\.)/g, '$1<br><br>$2')
                
                // 处理冒号后的换行（如果后面跟着列表）
                .replace(/([：:])\s*(✅|❌|[1-9]\.)/g, '$1<br><br>$2')
                
                // 处理感叹号和表情符号后的换行
                .replace(/([！!😊🎮])\s*([A-Z]|[1-9]\.|✅|❌)/g, '$1<br><br>$2')
                
                // 清理多余的换行
                .replace(/(<br>\s*){3,}/g, '<br><br>')
                .replace(/^(<br>\s*)+/, '')
                .replace(/(<br>\s*)+$/, '');
            
            return formattedText;
        }

        async function testAPI(question, gameType) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在请求API...';
            
            try {
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: question,
                        game_type: gameType
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const originalText = data.data.advice;
                    const formattedText = formatText(originalText);
                    
                    resultDiv.innerHTML = `
                        <h4>原始文本：</h4>
                        <div style="background: #ffe6e6; padding: 10px; margin: 10px 0; border-radius: 5px;">
                            ${originalText}
                        </div>
                        
                        <h4>格式化后：</h4>
                        <div style="background: #e6ffe6; padding: 10px; margin: 10px 0; border-radius: 5px;">
                            ${formattedText}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `错误: ${data.error}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `网络错误: ${error.message}`;
            }
        }

        function testFormatting() {
            testAPI('王者荣耀射手位怎么玩？请详细说明', 'MOBA');
        }

        function testFormatting2() {
            testAPI('原神新手攻略，请详细说明', 'RPG');
        }

        function testFormatting3() {
            testAPI('我的世界建筑技巧有哪些？', '沙盒');
        }
    </script>
</body>
</html>
