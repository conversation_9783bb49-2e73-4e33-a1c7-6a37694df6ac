"""
测试文本格式化功能
"""
import requests
import json

def test_formatting():
    """测试API返回的文本格式化"""
    print("=== 测试文本格式化功能 ===\n")
    
    # 测试问题
    test_question = "王者荣耀射手位怎么玩？请详细说明操作技巧和装备推荐"
    
    print(f"测试问题: {test_question}")
    print("-" * 50)
    
    try:
        # 调用API
        response = requests.post(
            "http://localhost:5000/api/ask",
            headers={"Content-Type": "application/json"},
            json={
                "question": test_question,
                "game_type": "MOBA"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                advice = data['data']['advice']
                sources = data['data']['relevant_sources']
                
                print("✅ API调用成功")
                print(f"回答长度: {len(advice)} 字符")
                print(f"相关来源: {len(sources)} 个")
                print()
                
                print("📝 原始回答:")
                print("-" * 30)
                print(advice)
                print()
                
                print("🎨 格式化后的回答:")
                print("-" * 30)
                formatted_advice = format_text_for_display(advice)
                print(formatted_advice)
                print()
                
                if sources:
                    print("📚 相关来源:")
                    for i, source in enumerate(sources, 1):
                        print(f"{i}. 相似度: {source['similarity']:.3f}")
                        print(f"   内容: {source['content'][:100]}...")
                        print(f"   来源: {source['source']}")
                        print()
                
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def format_text_for_display(text):
    """
    格式化文本用于控制台显示
    """
    if not text:
        return text
    
    # 基本格式化
    formatted = text
    
    # 在数字列表前添加换行
    import re
    formatted = re.sub(r'([。！？])\s*(\d+\.)', r'\1\n\n\2', formatted)
    
    # 在符号列表前添加换行
    formatted = re.sub(r'([。！？])\s*(✅|❌|🎮|📊)', r'\1\n\n\2', formatted)
    
    # 在标题前添加换行
    formatted = re.sub(r'([。！？])\s*(###?\s*[^。！？\n]+)', r'\1\n\n\2', formatted)
    
    # 处理粗体标记（在控制台中用大写显示）
    formatted = re.sub(r'\*\*([^*]+)\*\*', r'【\1】', formatted)
    
    # 清理多余的空行
    formatted = re.sub(r'\n{3,}', '\n\n', formatted)
    
    return formatted.strip()

def test_simple_formatting():
    """测试简单的格式化功能"""
    print("\n=== 测试简单格式化 ===\n")
    
    # 模拟一个典型的AI回答
    sample_text = """你好！我是专业的MOBA游戏攻略助手。**王者荣耀射手位攻略**如下： 1. **对线期技巧**：优先补兵，避免无意义消耗，利用草丛卡视野。 2. **装备推荐**：攻速鞋→无尽战刃→影刃→破晓→贤者的庇护。 3. **团战站位**：保持安全距离，优先攻击前排。 ✅ 记住走A技巧很重要 ✅ 观察小地图预判gank ❌ 不要贪刀导致被抓 希望这些建议对你有帮助！"""
    
    print("原始文本:")
    print("-" * 30)
    print(sample_text)
    print()
    
    print("格式化后:")
    print("-" * 30)
    formatted = format_text_for_display(sample_text)
    print(formatted)

def main():
    """主函数"""
    # 先测试简单格式化
    test_simple_formatting()
    
    # 再测试API调用
    print("\n" + "="*60)
    test_formatting()

if __name__ == "__main__":
    main()
