// 游戏攻略助手前端JavaScript

class GameAssistant {
    constructor() {
        this.isLoading = false;
        this.init();
    }

    init() {
        // 初始化事件监听器
        this.setupEventListeners();
        
        // 加载建议问题
        this.loadSuggestions();
        
        // 聚焦输入框
        document.getElementById('userInput').focus();
    }

    setupEventListeners() {
        // 游戏类型变化时重新加载建议
        document.getElementById('gameType').addEventListener('change', () => {
            this.loadSuggestions();
        });
    }

    // 发送消息
    async sendMessage() {
        if (this.isLoading) return;

        const input = document.getElementById('userInput');
        const question = input.value.trim();
        
        if (!question) {
            this.showAlert('请输入问题', 'warning');
            return;
        }

        // 显示用户消息
        this.addMessage(question, 'user');
        
        // 清空输入框
        input.value = '';
        
        // 显示加载状态
        this.isLoading = true;
        const loadingMessage = this.addMessage('正在思考中...', 'assistant', true);
        
        try {
            const gameType = document.getElementById('gameType').value;
            
            const response = await fetch('/api/ask', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question: question,
                    game_type: gameType
                })
            });

            const data = await response.json();
            
            // 移除加载消息
            loadingMessage.remove();
            
            if (data.success) {
                // 显示助手回复
                this.addMessage(data.data.advice, 'assistant');
                
                // 显示相关来源（如果有）
                if (data.data.relevant_sources && data.data.relevant_sources.length > 0) {
                    this.addSourcesMessage(data.data.relevant_sources);
                }
            } else {
                this.addMessage(`抱歉，出现了错误：${data.error}`, 'assistant');
            }
            
        } catch (error) {
            loadingMessage.remove();
            this.addMessage('抱歉，网络连接出现问题，请稍后再试。', 'assistant');
            console.error('Error:', error);
        } finally {
            this.isLoading = false;
        }
    }

    // 格式化文本，改善排版
    formatText(text) {
        if (!text) return '';

        // 处理各种格式化需求
        let formattedText = text
            // 处理数字列表（1. 2. 3.）
            .replace(/(\d+\.\s*\*\*[^*]+\*\*)/g, '<br><br>$1')
            .replace(/(\d+\.\s*[^1-9\n]*?)(?=\d+\.|$)/g, '<br><br>$1')

            // 处理项目符号列表（✅ ❌ 等）
            .replace(/(✅|❌|🎮|📊|🔍|⚠️|💡|🎯|📋|🚀)\s*/g, '<br><br>$1 ')

            // 处理Markdown标题（### ## #）
            .replace(/###\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')
            .replace(/##\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')
            .replace(/#\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')

            // 处理粗体文本（**text**）
            .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')

            // 处理问号后的换行
            .replace(/([？?])\s*([1-9]\.)/g, '$1<br><br>$2')

            // 处理冒号后的换行（如果后面跟着列表）
            .replace(/([：:])\s*(✅|❌|[1-9]\.)/g, '$1<br><br>$2')

            // 处理感叹号和表情符号后的换行
            .replace(/([！!😊🎮])\s*([A-Z]|[1-9]\.|✅|❌)/g, '$1<br><br>$2')

            // 清理多余的换行
            .replace(/(<br>\s*){3,}/g, '<br><br>')
            .replace(/^(<br>\s*)+/, '')
            .replace(/(<br>\s*)+$/, '');

        return formattedText;
    }

    // 添加消息到聊天区域
    addMessage(text, sender, isLoading = false) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const icon = sender === 'user' ? 'bi-person-fill' : 'bi-robot';
        const loadingHtml = isLoading ? '<span class="loading"></span>' : '';

        // 对助手回复进行格式化
        const formattedText = sender === 'assistant' && !isLoading ?
            this.formatText(text) : text;

        messageDiv.innerHTML = `
            <div class="message-content">
                <i class="bi ${icon}"></i>
                <div class="message-text">
                    ${loadingHtml}${formattedText}
                </div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        return messageDiv;
    }

    // 添加相关来源信息
    addSourcesMessage(sources) {
        const messagesContainer = document.getElementById('chatMessages');
        const sourceDiv = document.createElement('div');
        sourceDiv.className = 'message assistant-message';
        
        let sourcesHtml = '<div class="sources-section">';
        sourcesHtml += '<div class="sources-title"><i class="bi bi-book"></i> 相关参考资料：</div>';
        
        sources.forEach((source, index) => {
            sourcesHtml += `
                <div class="source-item">
                    <div>${source.content}</div>
                    <div class="source-meta">
                        相似度: ${(source.similarity * 100).toFixed(1)}% | 
                        来源: ${source.source}
                    </div>
                </div>
            `;
        });
        
        sourcesHtml += '</div>';
        
        sourceDiv.innerHTML = `
            <div class="message-content">
                <i class="bi bi-robot"></i>
                <div class="message-text">
                    ${sourcesHtml}
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(sourceDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 加载建议问题
    async loadSuggestions() {
        try {
            const gameType = document.getElementById('gameType').value;
            const response = await fetch(`/api/suggestions?game_type=${encodeURIComponent(gameType)}`);
            const data = await response.json();
            
            if (data.success) {
                this.displaySuggestions(data.data);
            }
        } catch (error) {
            console.error('加载建议失败:', error);
        }
    }

    // 显示建议问题
    displaySuggestions(suggestions) {
        const container = document.getElementById('suggestions');
        container.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = suggestion;
            item.onclick = () => this.selectSuggestion(suggestion);
            container.appendChild(item);
        });
    }

    // 选择建议问题
    selectSuggestion(suggestion) {
        document.getElementById('userInput').value = suggestion;
        document.getElementById('userInput').focus();
    }

    // 处理键盘事件
    handleKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendMessage();
        }
    }

    // 显示提示信息
    showAlert(message, type = 'info') {
        // 创建提示框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
}

// 全局函数
let gameAssistant;

document.addEventListener('DOMContentLoaded', function() {
    gameAssistant = new GameAssistant();
});

function sendMessage() {
    gameAssistant.sendMessage();
}

function handleKeyPress(event) {
    gameAssistant.handleKeyPress(event);
}

// 知识库管理功能
function showKnowledgeModal() {
    const modal = new bootstrap.Modal(document.getElementById('knowledgeModal'));
    modal.show();
}

async function loadKnowledge() {
    const datasetId = document.getElementById('datasetId').value.trim();
    const subsetName = document.getElementById('subsetName').value.trim();
    const forceReload = document.getElementById('forceReload').checked;
    
    if (!datasetId) {
        gameAssistant.showAlert('请输入数据集ID', 'warning');
        return;
    }
    
    try {
        gameAssistant.showAlert('正在加载知识库，请稍候...', 'info');
        
        const response = await fetch('/api/knowledge/load', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dataset_id: datasetId,
                subset_name: subsetName || null,
                force_reload: forceReload
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            gameAssistant.showAlert('知识库加载成功！', 'success');
            bootstrap.Modal.getInstance(document.getElementById('knowledgeModal')).hide();
        } else {
            gameAssistant.showAlert(`加载失败：${data.error}`, 'danger');
        }
        
    } catch (error) {
        gameAssistant.showAlert('网络错误，请稍后再试', 'danger');
        console.error('Error:', error);
    }
}

// 统计信息功能
function showStatsModal() {
    const modal = new bootstrap.Modal(document.getElementById('statsModal'));
    modal.show();
    loadStats();
}

async function loadStats() {
    try {
        const response = await fetch('/api/assistant/stats');
        const data = await response.json();
        
        if (data.success) {
            displayStats(data.data);
        } else {
            document.getElementById('statsContent').innerHTML = 
                `<div class="alert alert-danger">加载统计信息失败：${data.error}</div>`;
        }
        
    } catch (error) {
        document.getElementById('statsContent').innerHTML = 
            '<div class="alert alert-danger">网络错误，无法加载统计信息</div>';
        console.error('Error:', error);
    }
}

function displayStats(stats) {
    const content = document.getElementById('statsContent');
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <div class="stats-card">
                    <h6><i class="bi bi-chat-dots"></i> 对话统计</h6>
                    <div class="stats-value">${stats.conversation_count}</div>
                    <small>总对话次数</small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stats-card">
                    <h6><i class="bi bi-database"></i> 知识库统计</h6>
                    <div class="stats-value">${stats.knowledge_base_stats.vector_store?.count || 0}</div>
                    <small>知识条目数量</small>
                </div>
            </div>
        </div>
        <div class="mt-3">
            <h6>详细信息</h6>
            <pre class="bg-light p-3 rounded">${JSON.stringify(stats, null, 2)}</pre>
        </div>
    `;
    
    content.innerHTML = html;
}

// 清空历史记录
async function clearHistory() {
    if (!confirm('确定要清空对话历史吗？')) {
        return;
    }
    
    try {
        const response = await fetch('/api/clear_history', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            gameAssistant.showAlert('对话历史已清空', 'success');
            // 清空聊天界面（保留欢迎消息）
            const messagesContainer = document.getElementById('chatMessages');
            const welcomeMessage = messagesContainer.firstElementChild;
            messagesContainer.innerHTML = '';
            messagesContainer.appendChild(welcomeMessage);
        } else {
            gameAssistant.showAlert(`清空失败：${data.error}`, 'danger');
        }
        
    } catch (error) {
        gameAssistant.showAlert('网络错误，请稍后再试', 'danger');
        console.error('Error:', error);
    }
}
